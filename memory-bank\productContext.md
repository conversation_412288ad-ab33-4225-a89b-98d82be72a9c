# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-20 15:04:33 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

* 系统建模课程项目，专注于二维龙门起重机系统的建模、仿真与控制研究
* 通过MATLAB/Simulink平台设计并验证PID和LQR两种控制策略的性能
* 为实际工程应用提供理论依据和仿真验证

## Key Features

* 系统建模：基于欧拉-拉格朗日方法建立龙门起重机非线性动力学模型
* 控制器设计：实现双闭环PID控制器和LQR最优控制器
* 仿真验证：通过Simulink进行轨迹跟踪和抗干扰性能对比分析
* 实验指导：提供完整的实验方案设计和详细的操作指导文档
* 代码实现：包含完整的MATLAB建模代码和图片生成代码
* 建模指导：详细的Simulink建模步骤和快速参考文档

## Overall Architecture

* 理论层：数学建模和控制理论分析
* 实现层：MATLAB代码实现和Simulink模型搭建
* 验证层：仿真实验和性能对比分析
* 文档层：实验指导和操作参考文档

[2025-07-20 17:26:49] - New feature: 完成龙门起重机实验方案设计和Simulink建模指导文档