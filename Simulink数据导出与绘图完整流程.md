# Simulink数据导出与MATLAB绘图完整流程

## 问题解答总览

### 1. **数据导出配置** - 必须添加To Workspace模块
### 2. **MATLAB绘图实现** - 需要先从Simulink获取数据，不能直接运行
### 3. **数据变量对应关系** - 需要数据提取步骤

## 第一部分：To Workspace模块配置

### 必要性说明
**是的，必须在每个Simulink模型中添加To Workspace模块来导出仿真数据。**

### 具体配置参数

#### PID控制模型 (crane_pid_control.slx)
```
Variable name: simout_pid
Limit data points to last: 2000
Save format: Structure with time
Sample time: 0 (继承系统采样时间)
```

#### LQR控制模型 (crane_lqr_control.slx)
```
Variable name: simout_lqr
Limit data points to last: 2000
Save format: Structure with time
Sample time: 0 (继承系统采样时间)
```

#### 无控制模型 (crane_no_control.slx)
```
Variable name: simout_no
Limit data points to last: 2000
Save format: Structure with time
Sample time: 0 (继承系统采样时间)
```

### Simulink中的连接方法
1. 将Demux模块的输出端口连接到To Workspace模块的输入端口
2. Demux输出包含两个信号：[位置, 摆角]
3. To Workspace模块会自动记录时间向量和信号值

## 第二部分：数据流程说明

### 完整数据流程
```
Simulink仿真 → To Workspace模块 → MATLAB工作空间变量 → 数据提取 → 绘图代码
```

### 数据结构说明
To Workspace模块导出的数据结构：
```matlab
simout_pid = 
    time: [n×1 double]           % 时间向量
    signals: [1×1 struct]
        values: [n×2 double]     % 信号值矩阵
        dimensions: 2
        label: ''
```

## 第三部分：数据提取代码

### 完整的数据提取脚本
```matlab
%% 数据提取脚本 - 必须在仿真完成后运行
% 前提：已经运行了三个Simulink模型并导出了数据

% 检查数据是否存在
if ~exist('simout_no', 'var')
    error('请先运行 crane_no_control.slx 模型');
end
if ~exist('simout_pid', 'var')
    error('请先运行 crane_pid_control.slx 模型');
end
if ~exist('simout_lqr', 'var')
    error('请先运行 crane_lqr_control.slx 模型');
end

% 提取无控制系统数据
t_no = simout_no.time;                    % 时间向量
x_no = simout_no.signals.values(:,1);     % 位置数据（第1列）
theta_no = simout_no.signals.values(:,2); % 摆角数据（第2列）

% 提取PID控制系统数据
t_pid = simout_pid.time;                    % 时间向量
x_pid = simout_pid.signals.values(:,1);     % 位置数据（第1列）
theta_pid = simout_pid.signals.values(:,2); % 摆角数据（第2列）

% 提取LQR控制系统数据
t_lqr = simout_lqr.time;                    % 时间向量
x_lqr = simout_lqr.signals.values(:,1);     % 位置数据（第1列）
theta_lqr = simout_lqr.signals.values(:,2); % 摆角数据（第2列）

% 数据验证
fprintf('数据提取完成:\n');
fprintf('无控制系统数据点数: %d\n', length(t_no));
fprintf('PID控制系统数据点数: %d\n', length(t_pid));
fprintf('LQR控制系统数据点数: %d\n', length(t_lqr));
fprintf('仿真时间范围: %.2f - %.2f 秒\n', min(t_pid), max(t_pid));
```

## 第四部分：绘图代码

### 位置响应对比图生成
```matlab
%% 生成位置响应对比图 (fig6_pos_response.png)
% 前提：必须先运行数据提取脚本

figure('Position', [100, 100, 1000, 600]);
plot(t_no, x_no, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
hold on;
plot(t_pid, x_pid, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
plot(t_lqr, x_lqr, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');

% 添加参考线
step_time = 1;  % 阶跃输入时间
plot([step_time, step_time], [0, 1.2], 'k:', 'LineWidth', 1, 'DisplayName', '参考输入时刻');
plot([0, 15], [1, 1], 'k:', 'LineWidth', 1, 'DisplayName', '目标位置');

xlabel('时间 (s)', 'FontSize', 12);
ylabel('小车位置 (m)', 'FontSize', 12);
title('不同控制器下的小车位置响应曲线', 'FontSize', 14);
legend('Location', 'best');
grid on;
xlim([0, 15]);
ylim([0, 1.2]);

% 保存图片
print('fig6_pos_response', '-dpng', '-r300');
fprintf('位置响应图已保存: fig6_pos_response.png\n');
```

### 摆角响应对比图生成
```matlab
%% 生成摆角响应对比图 (fig7_angle_response.png)
% 前提：必须先运行数据提取脚本

figure('Position', [100, 100, 1000, 600]);
plot(t_no, theta_no*180/pi, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
hold on;
plot(t_pid, theta_pid*180/pi, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
plot(t_lqr, theta_lqr*180/pi, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');

% 添加参考线
plot([0, 15], [0, 0], 'k:', 'LineWidth', 1, 'DisplayName', '目标摆角');

xlabel('时间 (s)', 'FontSize', 12);
ylabel('负载摆角 (度)', 'FontSize', 12);
title('不同控制器下的负载摆角响应曲线', 'FontSize', 14);
legend('Location', 'best');
grid on;
xlim([0, 15]);

% 保存图片
print('fig7_angle_response', '-dpng', '-r300');
fprintf('摆角响应图已保存: fig7_angle_response.png\n');
```

## 第五部分：完整操作步骤

### 步骤1：Simulink模型准备
1. 创建三个独立的Simulink模型文件
2. 在每个模型中添加To Workspace模块
3. 正确配置变量名和参数

### 步骤2：运行仿真（按顺序执行）
```matlab
% 运行仿真模型
sim('crane_no_control');    % 导出simout_no
sim('crane_pid_control');   % 导出simout_pid
sim('crane_lqr_control');   % 导出simout_lqr
```

### 步骤3：数据提取
```matlab
% 运行数据提取脚本
run('data_extraction_script.m');
```

### 步骤4：生成图片
```matlab
% 运行绘图脚本
run('plot_position_response.m');
run('plot_angle_response.m');
```

## 关键注意事项

### 数据结构理解
- **simout_xxx.time**：时间向量
- **simout_xxx.signals.values(:,1)**：位置数据（第1列）
- **simout_xxx.signals.values(:,2)**：摆角数据（第2列）

### 常见错误及解决
1. **"变量未定义"错误**：确保先运行Simulink仿真
2. **"维度不匹配"错误**：检查Demux输出连接
3. **"空数据"错误**：检查To Workspace模块配置

### 文件管理建议
- 将数据提取代码保存为独立的.m文件
- 将绘图代码分别保存为独立的.m文件
- 建立清晰的文件命名规范

## 第六部分：脚本文件组织建议

### 建议的文件结构
```
项目文件夹/
├── crane_system_modeling.m          % 系统建模主脚本
├── crane_controller_design.m        % 控制器设计脚本
├── crane_no_control.slx             % 无控制Simulink模型
├── crane_pid_control.slx            % PID控制Simulink模型
├── crane_lqr_control.slx            % LQR控制Simulink模型
├── data_extraction.m                % 数据提取脚本
├── plot_responses.m                 % 绘图脚本
└── generated_figures/               % 生成的图片文件夹
    ├── fig6_pos_response.png
    └── fig7_angle_response.png
```

### 主控制脚本示例
```matlab
%% 龙门起重机仿真实验主控制脚本
% 按顺序执行所有实验步骤

clear; clc; close all;

%% 步骤1：系统建模和控制器设计
fprintf('步骤1：执行系统建模和控制器设计...\n');
run('crane_system_modeling.m');
run('crane_controller_design.m');

%% 步骤2：运行Simulink仿真
fprintf('步骤2：运行Simulink仿真...\n');
sim('crane_no_control');
fprintf('无控制系统仿真完成\n');

sim('crane_pid_control');
fprintf('PID控制系统仿真完成\n');

sim('crane_lqr_control');
fprintf('LQR控制系统仿真完成\n');

%% 步骤3：数据提取和绘图
fprintf('步骤3：数据提取和绘图...\n');
run('data_extraction.m');
run('plot_responses.m');

fprintf('实验完成！请查看generated_figures文件夹中的结果图片。\n');
```

### 数据验证脚本示例
```matlab
%% 数据完整性验证脚本
function validate_simulation_data()
    % 检查必要变量是否存在
    required_vars = {'simout_no', 'simout_pid', 'simout_lqr'};

    for i = 1:length(required_vars)
        if ~exist(required_vars{i}, 'var')
            error('缺少变量: %s，请先运行对应的Simulink模型', required_vars{i});
        end
    end

    % 检查数据维度
    if size(simout_pid.signals.values, 2) ~= 2
        error('PID仿真数据维度错误，应该包含位置和摆角两个信号');
    end

    fprintf('数据验证通过！\n');
end
```

通过这个完整流程和文件组织结构，您可以确保Simulink仿真数据正确传递给MATLAB绘图函数，生成高质量的实验结果图表。
