# Simulink建模操作快速参考

## 快速启动流程

### 1. 环境准备
```matlab
% 在MATLAB中执行以下命令
clear; clc; close all;
% 运行系统参数定义代码
% 运行控制器设计代码
simulink  % 打开Simulink
```

### 2. 创建新模型
- 方法1：命令行 `new_system('model_name'); open_system('model_name')`
- 方法2：Simulink Start Page → Blank Model

## 模块库快速导航

### 常用模块位置
| 模块名称 | 库路径 | 用途 |
|---------|--------|------|
| State-Space | Simulink > Continuous | 系统模型 |
| PID Controller | Simulink > Continuous | PID控制器 |
| Sum | Simulink > Math Operations | 求和运算 |
| Gain | Simulink > Math Operations | 增益放大 |
| Integrator | Simulink > Continuous | 积分器 |
| Step | Simulink > Sources | 阶跃输入 |
| Scope | Simulink > Sinks | 波形显示 |
| To Workspace | Simulink > Sinks | 数据导出 |
| Demux | Simulink > Signal Routing | 信号分离 |

### 快速添加模块
1. **拖拽方式**：从Library Browser拖拽到模型窗口
2. **搜索方式**：在模型窗口中直接输入模块名称
3. **快捷键**：Ctrl+Shift+A 打开模块搜索

## PID控制系统搭建检查清单

### 模块配置检查
- [ ] Step模块：Step time=1, Final value=1
- [ ] Sum1模块：List of signs = |+-
- [ ] Sum2模块：List of signs = |++
- [ ] 位置PID：P=50, I=5, D=10
- [ ] 摆角PID：P=100, I=10, D=20
- [ ] State-Space：A, B_matrix, C, D矩阵正确
- [ ] Demux：Number of outputs = 2

### 连接检查
- [ ] Step → Sum1(+)
- [ ] Sum1 → 位置PID
- [ ] 位置PID → Sum2(+)
- [ ] Sum2 → State-Space
- [ ] State-Space → Demux
- [ ] Demux(1) → Sum1(-)  [位置反馈]
- [ ] Demux(2) → 摆角PID
- [ ] 摆角PID → Sum2(+)

## LQR控制系统搭建检查清单

### 模块配置检查
- [ ] Step模块：Step time=1, Final value=1
- [ ] Sum模块：List of signs = |+-
- [ ] Integrator：Initial condition = 0
- [ ] Gain：Gain = -K (注意负号)
- [ ] State-Space：A, B_matrix, C, D矩阵正确
- [ ] Demux：Number of outputs = 2

### 连接检查
- [ ] Step → Sum(+)
- [ ] Sum → Integrator
- [ ] Integrator → State-Space(输入)
- [ ] State-Space(状态输出) → Gain
- [ ] Gain → Sum(-)  [状态反馈]
- [ ] State-Space(系统输出) → Demux
- [ ] Demux(1) → Sum(-)  [位置反馈]

## 仿真设置快速配置

### 通过界面设置
1. Simulation → Model Configuration Parameters
2. Solver选项卡：
   - Stop time: 15
   - Solver: ode45
   - Max step size: 0.01
   - Relative tolerance: 1e-3

### 通过命令设置
```matlab
model_name = 'your_model_name';
set_param(model_name, 'StopTime', '15');
set_param(model_name, 'Solver', 'ode45');
set_param(model_name, 'MaxStep', '0.01');
set_param(model_name, 'RelTol', '1e-3');
```

## 数据采集配置

### To Workspace模块设置
- Variable name: simout_pid 或 simout_lqr
- Limit data points to last: 2000
- Save format: Structure with time
- Sample time: 0 (继承)

### Scope模块优化
- 右键Scope → Configuration Properties
- General选项卡：
  - Number of input ports: 2
  - Time range: 15
- Display选项卡：
  - Show legend: 勾选
  - Show grid: 勾选

## 常见错误及快速解决

### 编译错误
| 错误信息 | 可能原因 | 解决方法 |
|---------|---------|---------|
| Undefined variable 'A' | 变量未定义 | 运行MATLAB初始化代码 |
| Dimension mismatch | 矩阵维度不匹配 | 检查A,B,C,D矩阵尺寸 |
| Algebraic loop | 代数环 | 添加Unit Delay模块 |

### 仿真结果异常
- **系统不稳定**：检查反馈极性，确认负反馈连接
- **响应过慢**：增大PID增益或调整LQR权重矩阵
- **振荡严重**：减小微分增益或增大R权重

## 模型保存和管理

### 文件命名规范
- PID控制模型：`crane_pid_control.slx`
- LQR控制模型：`crane_lqr_control.slx`
- 无控制模型：`crane_no_control.slx`

### 版本管理
- 每次重大修改前保存副本
- 使用有意义的文件名后缀（如_v1, _v2）
- 在模型中添加注释说明修改内容

## 快速调试技巧

### 信号监控
1. 双击信号线添加信号标签
2. 使用Display模块显示数值
3. 添加多个Scope监控不同信号

### 参数调整
1. 使用Slider Gain模块实时调整参数
2. 在MATLAB工作空间中修改变量后重新运行
3. 使用Model Explorer批量修改参数

### 性能分析
```matlab
% 仿真后分析代码
figure;
subplot(2,1,1);
plot(simout.time, simout.signals.values(:,1));
title('位置响应');
subplot(2,1,2);
plot(simout.time, simout.signals.values(:,2));
title('摆角响应');
```

## 模型优化建议

### 提高可读性
- 使用子系统封装复杂逻辑
- 添加注释和文档块
- 使用一致的模块布局

### 提高性能
- 避免不必要的数据记录
- 使用合适的数据类型
- 优化求解器设置

### 便于维护
- 参数化所有常数
- 使用Mask创建自定义模块
- 建立模块库复用常用组件

按照此快速参考指南，您可以高效地完成Simulink建模任务，确保模型的正确性和可靠性。
