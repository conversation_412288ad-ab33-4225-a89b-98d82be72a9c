# TeX Live 2025 配置修复指南

## 问题诊断
您遇到的错误表明 TeX Live 2025 的核心配置文件和路径系统存在问题：
- Fontconfig 无法加载默认配置文件
- 字体缓存目录不存在
- Kpathsea 路径搜索库无法正常工作

## 解决方案：重建 TeX Live 配置

### 第一步：检查 TeX Live 安装状态
打开命令提示符（以管理员身份运行），执行以下命令：

```cmd
tlmgr --version
```

如果命令无法识别，说明环境变量配置有问题。

### 第二步：重建配置数据库
按顺序执行以下命令：

```cmd
# 1. 重新生成文件名数据库
mktexlsr

# 2. 重建字体映射文件
updmap-sys --force

# 3. 重新生成格式文件
fmtutil-sys --all

# 4. 重建语言配置
tlmgr conf texmf TEXMFCACHE D:/Projects/texlive/2025/texmf-var
```

### 第三步：修复字体缓存
```cmd
# 创建字体缓存目录
mkdir "D:\Projects\texlive\2025\texmf-var\fonts\cache"

# 重建字体缓存
fc-cache -fv
```

### 第四步：修复 Fontconfig
```cmd
# 重新生成 fontconfig 配置
tlmgr conf texmf OSFONTDIR "C:/Windows/Fonts"

# 更新 fontconfig 缓存
fc-cache -r
```

### 第五步：验证修复结果
```cmd
# 测试 kpathsea
kpsewhich article.cls

# 测试字体系统
fc-list | head -5

# 测试编译
xelatex --version
```

## 如果上述方法无效

### 备选方案：重置 TeX Live 配置
```cmd
# 删除用户配置目录
rmdir /s "%USERPROFILE%\.texlive2025"

# 重新初始化
tlmgr init-usertree
```

### 环境变量检查
确保以下环境变量正确设置：
- `PATH` 包含：`D:\Projects\texlive\2025\bin\windows`
- `TEXMFCACHE`：`D:\Projects\texlive\2025\texmf-var`

## 编译测试
修复完成后，在您的项目目录中测试编译：

```cmd
cd "c:\Users\<USER>\Desktop\课程\系统建模"
xelatex ccc.tex
```

## 常见问题排查

### 如果仍然出现 fontconfig 错误：
1. 检查 `D:\Projects\texlive\2025\texmf-dist\fonts\conf\fonts.conf` 是否存在
2. 如果不存在，重新安装 TeX Live

### 如果 Kpathsea 仍然无法工作：
1. 检查 `texmf.cnf` 配置文件
2. 运行 `tlmgr conf` 查看当前配置

### 权限问题：
确保以管理员权限运行所有命令，特别是涉及系统目录的操作。

## 预防措施
- 定期运行 `tlmgr update --self --all` 保持系统更新
- 避免手动修改 TeX Live 核心配置文件
- 使用 `tlmgr` 管理包和配置，而不是手动操作
