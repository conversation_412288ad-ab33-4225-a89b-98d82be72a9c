# 数据提取和绘图脚本模板

## data_extraction.m - 数据提取脚本

```matlab
%% 龙门起重机仿真数据提取脚本
% 功能：从Simulink To Workspace模块导出的数据中提取时间序列
% 前提：必须先运行三个Simulink模型

function data_extraction()
    fprintf('开始数据提取...\n');
    
    %% 数据存在性检查
    required_vars = {'simout_no', 'simout_pid', 'simout_lqr'};
    
    for i = 1:length(required_vars)
        if ~evalin('base', sprintf('exist(''%s'', ''var'')', required_vars{i}))
            error('变量 %s 不存在，请先运行对应的Simulink模型', required_vars{i});
        end
    end
    
    %% 从base workspace获取数据
    simout_no = evalin('base', 'simout_no');
    simout_pid = evalin('base', 'simout_pid');
    simout_lqr = evalin('base', 'simout_lqr');
    
    %% 数据维度检查
    if size(simout_pid.signals.values, 2) ~= 2
        error('数据维度错误：应该包含位置和摆角两个信号');
    end
    
    %% 提取无控制系统数据
    t_no = simout_no.time;
    x_no = simout_no.signals.values(:,1);     % 位置（第1列）
    theta_no = simout_no.signals.values(:,2); % 摆角（第2列）
    
    %% 提取PID控制系统数据
    t_pid = simout_pid.time;
    x_pid = simout_pid.signals.values(:,1);     % 位置（第1列）
    theta_pid = simout_pid.signals.values(:,2); % 摆角（第2列）
    
    %% 提取LQR控制系统数据
    t_lqr = simout_lqr.time;
    x_lqr = simout_lqr.signals.values(:,1);     % 位置（第1列）
    theta_lqr = simout_lqr.signals.values(:,2); % 摆角（第2列）
    
    %% 将提取的数据保存到base workspace
    assignin('base', 't_no', t_no);
    assignin('base', 'x_no', x_no);
    assignin('base', 'theta_no', theta_no);
    
    assignin('base', 't_pid', t_pid);
    assignin('base', 'x_pid', x_pid);
    assignin('base', 'theta_pid', theta_pid);
    
    assignin('base', 't_lqr', t_lqr);
    assignin('base', 'x_lqr', x_lqr);
    assignin('base', 'theta_lqr', theta_lqr);
    
    %% 数据统计信息
    fprintf('数据提取完成！\n');
    fprintf('无控制系统数据点数: %d\n', length(t_no));
    fprintf('PID控制系统数据点数: %d\n', length(t_pid));
    fprintf('LQR控制系统数据点数: %d\n', length(t_lqr));
    fprintf('仿真时间范围: %.2f - %.2f 秒\n', min(t_pid), max(t_pid));
    
    %% 数据质量检查
    fprintf('\n数据质量检查：\n');
    fprintf('位置数据范围: %.3f - %.3f 米\n', min([x_pid; x_lqr]), max([x_pid; x_lqr]));
    fprintf('摆角数据范围: %.3f - %.3f 弧度\n', min([theta_pid; theta_lqr]), max([theta_pid; theta_lqr]));
    
    % 检查是否有异常值
    if any(isnan([x_pid; x_lqr])) || any(isinf([x_pid; x_lqr]))
        warning('位置数据中存在NaN或Inf值');
    end
    
    if any(isnan([theta_pid; theta_lqr])) || any(isinf([theta_pid; theta_lqr]))
        warning('摆角数据中存在NaN或Inf值');
    end
end
```

## plot_responses.m - 绘图脚本

```matlab
%% 龙门起重机仿真结果绘图脚本
% 功能：生成位置和摆角响应对比图
% 前提：必须先运行data_extraction.m

function plot_responses()
    fprintf('开始生成对比图...\n');
    
    %% 检查数据是否存在
    required_vars = {'t_no', 'x_no', 'theta_no', 't_pid', 'x_pid', 'theta_pid', 't_lqr', 'x_lqr', 'theta_lqr'};
    
    for i = 1:length(required_vars)
        if ~evalin('base', sprintf('exist(''%s'', ''var'')', required_vars{i}))
            error('变量 %s 不存在，请先运行 data_extraction.m', required_vars{i});
        end
    end
    
    %% 从base workspace获取数据
    t_no = evalin('base', 't_no');
    x_no = evalin('base', 'x_no');
    theta_no = evalin('base', 'theta_no');
    
    t_pid = evalin('base', 't_pid');
    x_pid = evalin('base', 'x_pid');
    theta_pid = evalin('base', 'theta_pid');
    
    t_lqr = evalin('base', 't_lqr');
    x_lqr = evalin('base', 'x_lqr');
    theta_lqr = evalin('base', 'theta_lqr');
    
    %% 创建输出文件夹
    if ~exist('generated_figures', 'dir')
        mkdir('generated_figures');
    end
    
    %% 生成位置响应对比图
    figure('Position', [100, 100, 1000, 600]);
    plot(t_no, x_no, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
    hold on;
    plot(t_pid, x_pid, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
    plot(t_lqr, x_lqr, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');
    
    % 添加参考线
    step_time = 1;
    plot([step_time, step_time], [0, 1.2], 'k:', 'LineWidth', 1, 'DisplayName', '参考输入时刻');
    plot([0, 15], [1, 1], 'k:', 'LineWidth', 1, 'DisplayName', '目标位置');
    
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('小车位置 (m)', 'FontSize', 12);
    title('不同控制器下的小车位置响应曲线', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
    xlim([0, 15]);
    ylim([0, 1.2]);
    
    % 保存图片
    print('generated_figures/fig6_pos_response', '-dpng', '-r300');
    fprintf('位置响应图已保存: generated_figures/fig6_pos_response.png\n');
    
    %% 生成摆角响应对比图
    figure('Position', [100, 100, 1000, 600]);
    plot(t_no, theta_no*180/pi, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
    hold on;
    plot(t_pid, theta_pid*180/pi, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
    plot(t_lqr, theta_lqr*180/pi, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');
    
    % 添加参考线
    plot([0, 15], [0, 0], 'k:', 'LineWidth', 1, 'DisplayName', '目标摆角');
    
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('负载摆角 (度)', 'FontSize', 12);
    title('不同控制器下的负载摆角响应曲线', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
    xlim([0, 15]);
    
    % 保存图片
    print('generated_figures/fig7_angle_response', '-dpng', '-r300');
    fprintf('摆角响应图已保存: generated_figures/fig7_angle_response.png\n');
    
    %% 生成组合图（可选）
    figure('Position', [100, 100, 1200, 800]);
    
    % 上子图：位置响应
    subplot(2,1,1);
    plot(t_no, x_no, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
    hold on;
    plot(t_pid, x_pid, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
    plot(t_lqr, x_lqr, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');
    plot([1, 1], [0, 1.2], 'k:', 'LineWidth', 1);
    plot([0, 15], [1, 1], 'k:', 'LineWidth', 1);
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('小车位置 (m)', 'FontSize', 12);
    title('小车位置响应对比', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
    xlim([0, 15]);
    ylim([0, 1.2]);
    
    % 下子图：摆角响应
    subplot(2,1,2);
    plot(t_no, theta_no*180/pi, 'r--', 'LineWidth', 2, 'DisplayName', '无控制');
    hold on;
    plot(t_pid, theta_pid*180/pi, 'b-', 'LineWidth', 2, 'DisplayName', 'PID控制');
    plot(t_lqr, theta_lqr*180/pi, 'g-', 'LineWidth', 2, 'DisplayName', 'LQR控制');
    plot([0, 15], [0, 0], 'k:', 'LineWidth', 1);
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('负载摆角 (度)', 'FontSize', 12);
    title('负载摆角响应对比', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
    xlim([0, 15]);
    
    % 保存组合图
    print('generated_figures/fig_combined_response', '-dpng', '-r300');
    fprintf('组合响应图已保存: generated_figures/fig_combined_response.png\n');
    
    fprintf('所有图片生成完成！\n');
end
```

## main_experiment.m - 主控制脚本

```matlab
%% 龙门起重机仿真实验主控制脚本
% 完整的实验流程控制

clear; clc; close all;

fprintf('=== 龙门起重机仿真实验开始 ===\n\n');

try
    %% 步骤1：系统建模和控制器设计
    fprintf('步骤1：执行系统建模和控制器设计...\n');
    if exist('crane_system_modeling.m', 'file')
        run('crane_system_modeling.m');
        fprintf('系统建模完成\n');
    else
        warning('未找到 crane_system_modeling.m 文件');
    end
    
    if exist('crane_controller_design.m', 'file')
        run('crane_controller_design.m');
        fprintf('控制器设计完成\n');
    else
        warning('未找到 crane_controller_design.m 文件');
    end
    
    %% 步骤2：运行Simulink仿真
    fprintf('\n步骤2：运行Simulink仿真...\n');
    
    % 检查模型文件是否存在
    models = {'crane_no_control.slx', 'crane_pid_control.slx', 'crane_lqr_control.slx'};
    for i = 1:length(models)
        if ~exist(models{i}, 'file')
            error('未找到Simulink模型文件: %s', models{i});
        end
    end
    
    % 运行仿真
    sim('crane_no_control');
    fprintf('无控制系统仿真完成\n');
    
    sim('crane_pid_control');
    fprintf('PID控制系统仿真完成\n');
    
    sim('crane_lqr_control');
    fprintf('LQR控制系统仿真完成\n');
    
    %% 步骤3：数据提取和绘图
    fprintf('\n步骤3：数据提取和绘图...\n');
    
    data_extraction();
    plot_responses();
    
    fprintf('\n=== 实验完成！ ===\n');
    fprintf('请查看 generated_figures 文件夹中的结果图片。\n');
    
catch ME
    fprintf('\n实验过程中出现错误：\n');
    fprintf('错误信息：%s\n', ME.message);
    fprintf('错误位置：%s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end
```

## 使用说明

### 文件保存方式
1. 将上述三个脚本分别保存为：
   - `data_extraction.m`
   - `plot_responses.m`  
   - `main_experiment.m`

### 执行顺序
1. 首先运行系统建模和控制器设计脚本
2. 确保三个Simulink模型文件存在并正确配置
3. 运行 `main_experiment.m` 执行完整流程
4. 或者分步执行：先运行仿真，再运行数据提取和绘图脚本

### 输出结果
- 所有图片将保存在 `generated_figures/` 文件夹中
- 包含位置响应图、摆角响应图和组合图
- 控制台会显示详细的执行进度和数据统计信息
